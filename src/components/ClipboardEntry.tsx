import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  IconButton,
  Box,
  Chip,
  Tooltip,
  Button,
} from '@mui/material';
import {
  Star,
  StarBorder,
  Delete,
  ContentCopy,
  Image,
  InsertDriveFile,
  ExpandMore,
  ExpandLess,
} from '@mui/icons-material';
import { useAppDispatch } from '@/store';
import { toggleFavorite, deleteEntry } from '@/store/slices/clipboardSlice';
import type { ClipboardEntry as ClipboardEntryType } from '@/types/clipboard';

interface ClipboardEntryProps {
  entry: ClipboardEntryType;
  isSelected: boolean;
  onClick: () => void;
  onCopy: () => void;
}

const ClipboardEntry: React.FC<ClipboardEntryProps> = ({
  entry,
  isSelected,
  onClick,
  onCopy,
}) => {
  const dispatch = useAppDispatch();
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggleFavorite = (e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(toggleFavorite(entry.id));
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    dispatch(deleteEntry(entry.id));
  };

  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation();
    onCopy();
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const minutes = Math.floor(diffInHours * 60);
      return `${minutes}m ago`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const getTypeIcon = () => {
    switch (entry.type) {
      case 'image':
        return <Image fontSize="small" />;
      case 'file':
        return <InsertDriveFile fontSize="small" />;
      default:
        return null;
    }
  };

  const handleToggleExpand = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  const shouldShowExpandButton = entry.content.length > 200;
  const displayContent = isExpanded ? entry.content : entry.content.substring(0, 200);

  return (
    <Card
      sx={{
        mb: 1,
        cursor: 'pointer',
        border: isSelected ? 2 : 1,
        borderColor: isSelected ? 'primary.main' : 'divider',
        backgroundColor: isSelected ? 'action.selected' : 'background.paper',
        transition: 'all 0.2s ease-in-out',
        '&:hover': {
          backgroundColor: 'action.hover',
          borderColor: 'primary.light',
        },
      }}
      onClick={onClick}
    >
      <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Box display="flex" alignItems="center" gap={1} flex={1}>
            {getTypeIcon()}
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontSize: '0.75rem' }}
            >
              {formatTimestamp(entry.timestamp)}
            </Typography>
            {entry.metadata?.source && (
              <Chip
                label={entry.metadata.source}
                size="small"
                variant="outlined"
                sx={{ height: 20, fontSize: '0.7rem' }}
              />
            )}
          </Box>
          
          <Box display="flex" gap={0.5}>
            <Tooltip title={entry.isFavorite ? 'Remove from favorites' : 'Add to favorites'}>
              <IconButton
                size="small"
                onClick={handleToggleFavorite}
                color={entry.isFavorite ? 'warning' : 'default'}
              >
                {entry.isFavorite ? <Star fontSize="small" /> : <StarBorder fontSize="small" />}
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Copy to clipboard">
              <IconButton size="small" onClick={handleCopy} color="primary">
                <ContentCopy fontSize="small" />
              </IconButton>
            </Tooltip>
            
            <Tooltip title="Delete entry">
              <IconButton size="small" onClick={handleDelete} color="error">
                <Delete fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        <Box>
          <Typography
            variant="body2"
            sx={{
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              fontFamily: entry.type === 'text' ? 'inherit' : 'monospace',
              fontSize: entry.type === 'text' ? '0.875rem' : '0.75rem',
              lineHeight: 1.4,
            }}
          >
            {displayContent}
            {shouldShowExpandButton && !isExpanded && '...'}
          </Typography>
          
          {shouldShowExpandButton && (
            <Button
              size="small"
              onClick={handleToggleExpand}
              startIcon={isExpanded ? <ExpandLess /> : <ExpandMore />}
              sx={{ mt: 1, minHeight: 'auto', p: 0.5 }}
            >
              {isExpanded ? 'Show Less' : 'Show More'}
            </Button>
          )}
        </Box>

        {entry.preview && entry.type === 'image' && (
          <Box mt={1}>
            <img
              src={entry.preview}
              alt="Clipboard preview"
              style={{
                maxWidth: '100%',
                maxHeight: 100,
                borderRadius: 4,
                objectFit: 'contain',
              }}
            />
          </Box>
        )}

        {entry.metadata?.size && (
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{ mt: 1, display: 'block' }}
          >
            Size: {(entry.metadata.size / 1024).toFixed(1)} KB
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

export default ClipboardEntry;
