import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  FormControlLabel,
  Switch,
  Alert,
  InputAdornment,
  Stack,
  IconButton,
} from '@mui/material';
import { Keyboard, Save, Restore, Edit } from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import { 
  loadPreferences, 
  savePreferences,
  setAutoStart,
  setShowInSystemTray,
  setMaxHistoryLength,
  updateShortcut
} from '@/store/slices/preferencesSlice';
import { invoke } from '@tauri-apps/api/core';
import ShortcutDialog from './ShortcutDialog';

const Settings: React.FC = () => {
  const dispatch = useAppDispatch();
  const preferences = useAppSelector((state) => state.preferences);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [shortcutDialog, setShortcutDialog] = useState<{
    open: boolean;
    key: string;
    title: string;
    currentShortcut: string;
  }>({
    open: false,
    key: '',
    title: '',
    currentShortcut: '',
  });

  // Load preferences on component mount
  useEffect(() => {
    dispatch(loadPreferences());
  }, [dispatch]);

  const handleSaveAllSettings = async () => {
    setSaveStatus('saving');
    try {
      await dispatch(savePreferences(preferences)).unwrap();
      
      // Update global shortcuts
      await invoke('update_global_shortcuts', { shortcuts: preferences.shortcuts });
      
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      console.error('Failed to save preferences:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const handleReset = async () => {
    try {
      await dispatch(loadPreferences()).unwrap();
      setSaveStatus('idle');
    } catch (error) {
      console.error('Failed to reset preferences:', error);
    }
  };

  const handleShortcutChange = async (key: string, value: string) => {
    try {
      // Update Redux store immediately
      dispatch(updateShortcut({ key: key as keyof typeof preferences.shortcuts, value }));
      
      // Save to backend immediately
      const updatedPreferences = {
        ...preferences,
        shortcuts: {
          ...preferences.shortcuts,
          [key]: value,
        },
      };
      await dispatch(savePreferences(updatedPreferences)).unwrap();
      
      // Update global shortcuts
      await invoke('update_global_shortcuts', { shortcuts: updatedPreferences.shortcuts });
    } catch (error) {
      console.error('Failed to update shortcut:', error);
    }
  };

  const handleAutoStartChange = async (enabled: boolean) => {
    try {
      // Update Redux store immediately
      dispatch(setAutoStart(enabled));
      
      // Apply auto-start setting immediately
      await invoke('set_auto_start', { enabled });
      
      // Save to backend
      const updatedPreferences = { ...preferences, autoStart: enabled };
      await dispatch(savePreferences(updatedPreferences)).unwrap();
    } catch (error) {
      console.error('Failed to update auto start setting:', error);
      // Revert on error
      dispatch(setAutoStart(!enabled));
    }
  };

  const handleSystemTrayChange = async (enabled: boolean) => {
    try {
      // Update Redux store immediately
      dispatch(setShowInSystemTray(enabled));
      
      // Apply system tray setting immediately
      await invoke('set_system_tray', { enabled });
      
      // Save to backend
      const updatedPreferences = { ...preferences, showInSystemTray: enabled };
      await dispatch(savePreferences(updatedPreferences)).unwrap();
    } catch (error) {
      console.error('Failed to update system tray setting:', error);
      // Revert on error
      dispatch(setShowInSystemTray(!enabled));
    }
  };

  const handleMaxHistoryLengthChange = async (value: number) => {
    try {
      // Update Redux store immediately
      dispatch(setMaxHistoryLength(value));
      
      // Save to backend
      const updatedPreferences = { ...preferences, maxHistoryLength: value };
      await dispatch(savePreferences(updatedPreferences)).unwrap();
    } catch (error) {
      console.error('Failed to update max history length:', error);
      // Revert on error
      dispatch(setMaxHistoryLength(preferences.maxHistoryLength));
    }
  };

  const openShortcutDialog = (key: string, title: string) => {
    setShortcutDialog({
      open: true,
      key,
      title,
      currentShortcut: preferences.shortcuts[key as keyof typeof preferences.shortcuts],
    });
  };

  const closeShortcutDialog = () => {
    setShortcutDialog({
      open: false,
      key: '',
      title: '',
      currentShortcut: '',
    });
  };

  const handleShortcutSave = (shortcut: string) => {
    handleShortcutChange(shortcutDialog.key, shortcut);
    closeShortcutDialog();
  };

  // Safety check to prevent undefined errors
  if (!preferences.shortcuts) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading settings...</Typography>
      </Box>
    );
  }

  return (
    <Box 
      sx={{ 
        height: '100%', 
        overflow: 'auto',
        p: 3,
      }}
    >
      <Typography variant="h5" gutterBottom>
        Settings
      </Typography>

      {/* Global Shortcuts */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <Keyboard />
            <Typography variant="h6">Global Shortcuts</Typography>
          </Box>
          
          <Stack spacing={2}>
            <Box display="flex" gap={2} flexWrap="wrap">
              <TextField
                label="Toggle App"
                value={preferences.shortcuts.toggleApp}
                onChange={(e) => handleShortcutChange('toggleApp', e.target.value)}
                size="small"
                helperText="Shortcut to open/close the app"
                sx={{ minWidth: 250 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Keyboard fontSize="small" />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => openShortcutDialog('toggleApp', 'Toggle App Shortcut')}
                        title="Record shortcut"
                      >
                        <Edit fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              
              <TextField
                label="Clear History"
                value={preferences.shortcuts.clearHistory}
                onChange={(e) => handleShortcutChange('clearHistory', e.target.value)}
                size="small"
                helperText="Shortcut to clear clipboard history"
                sx={{ minWidth: 250 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => openShortcutDialog('clearHistory', 'Clear History Shortcut')}
                        title="Record shortcut"
                      >
                        <Edit fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            
            <Box display="flex" gap={2} flexWrap="wrap">
              <TextField
                label="Toggle Favorite"
                value={preferences.shortcuts.toggleFavorite}
                onChange={(e) => handleShortcutChange('toggleFavorite', e.target.value)}
                size="small"
                helperText="Shortcut to favorite selected item"
                sx={{ minWidth: 250 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => openShortcutDialog('toggleFavorite', 'Toggle Favorite Shortcut')}
                        title="Record shortcut"
                      >
                        <Edit fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
              
              <TextField
                label="Search"
                value={preferences.shortcuts.search}
                onChange={(e) => handleShortcutChange('search', e.target.value)}
                size="small"
                helperText="Shortcut to focus search"
                sx={{ minWidth: 250 }}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        size="small"
                        onClick={() => openShortcutDialog('search', 'Search Shortcut')}
                        title="Record shortcut"
                      >
                        <Edit fontSize="small" />
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
          </Stack>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Shortcut Format:</strong> Use modifiers like CommandOrControl, Shift, Alt followed by a key.
              <br />
              Examples: "CommandOrControl+Shift+V", "Alt+C", "F1"
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      {/* General Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            General Settings
          </Typography>
          
          <Stack spacing={2}>
            <TextField
              type="number"
              label="Max History Length"
              value={preferences.maxHistoryLength}
              onChange={(e) => handleMaxHistoryLengthChange(parseInt(e.target.value) || 100)}
              size="small"
              helperText="Maximum number of clipboard entries to keep"
              inputProps={{ min: 10, max: 1000 }}
              sx={{ maxWidth: 300 }}
            />
            
            <Box display="flex" flexDirection="column" gap={2}>
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.autoStart}
                      onChange={(e) => handleAutoStartChange(e.target.checked)}
                    />
                  }
                  label="Auto Start"
                />
                <Typography variant="caption" color="text.secondary" display="block" ml={4}>
                  Start the app automatically when system boots
                </Typography>
              </Box>
              
              <Box>
                <FormControlLabel
                  control={
                    <Switch
                      checked={preferences.showInSystemTray}
                      onChange={(e) => handleSystemTrayChange(e.target.checked)}
                    />
                  }
                  label="Show in System Tray"
                />
                <Typography variant="caption" color="text.secondary" display="block" ml={4}>
                  Keep the app running in the system tray
                </Typography>
              </Box>
            </Box>
          </Stack>
        </CardContent>
      </Card>

      {/* Save/Reset Actions */}
      <Card>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center" flexWrap="wrap">
            <Button
              variant="contained"
              onClick={handleSaveAllSettings}
              disabled={saveStatus === 'saving'}
              startIcon={<Save />}
            >
              {saveStatus === 'saving' ? 'Saving...' : 'Save All Settings'}
            </Button>
            
            <Button
              variant="outlined"
              onClick={handleReset}
              startIcon={<Restore />}
            >
              Reset to Defaults
            </Button>
            
            {saveStatus === 'saved' && (
              <Alert severity="success" sx={{ py: 0 }}>
                Settings saved successfully!
              </Alert>
            )}
            
            {saveStatus === 'error' && (
              <Alert severity="error" sx={{ py: 0 }}>
                Failed to save settings. Please try again.
              </Alert>
            )}
          </Box>
        </CardContent>
      </Card>

      {/* Shortcut Dialog */}
      <ShortcutDialog
        open={shortcutDialog.open}
        onClose={closeShortcutDialog}
        onSave={handleShortcutSave}
        title={shortcutDialog.title}
        currentShortcut={shortcutDialog.currentShortcut}
      />
    </Box>
  );
};

export default Settings;
