import React, { useState, useEffect, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  Alert,
} from '@mui/material';
import { Keyboard } from '@mui/icons-material';

interface ShortcutDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (shortcut: string) => void;
  title: string;
  currentShortcut?: string;
}

interface KeyCombination {
  ctrl: boolean;
  alt: boolean;
  shift: boolean;
  meta: boolean;
  key: string;
}

const ShortcutDialog: React.FC<ShortcutDialogProps> = ({
  open,
  onClose,
  onSave,
  title,
  currentShortcut = '',
}) => {
  const [detectedKeys, setDetectedKeys] = useState<KeyCombination | null>(null);
  const [isListening, setIsListening] = useState(false);
  const [error, setError] = useState<string>('');

  const formatShortcut = (combination: KeyCombination): string => {
    const parts: string[] = [];
    
    if (combination.ctrl || combination.meta) {
      parts.push('CommandOrControl');
    }
    if (combination.shift) {
      parts.push('Shift');
    }
    if (combination.alt) {
      parts.push('Alt');
    }
    if (combination.key) {
      parts.push(combination.key);
    }
    
    return parts.join('+');
  };

  const formatDisplayShortcut = (shortcut: string): string => {
    return shortcut
      .replace(/CommandOrControl/g, '⌘/Ctrl')
      .replace(/Shift/g, '⇧')
      .replace(/Alt/g, '⌥')
      .replace(/\+/g, ' + ');
  };

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (!isListening) return;

    event.preventDefault();
    event.stopPropagation();

    // Ignore modifier keys by themselves
    if (['Control', 'Alt', 'Shift', 'Meta', 'Command'].includes(event.key)) {
      return;
    }

    // Map special keys
    let key = event.key;
    if (key === ' ') key = 'Space';
    if (key === 'Escape') key = 'Escape';
    if (key.startsWith('Arrow')) key = event.key;
    if (key === 'Enter') key = 'Return';
    if (key === 'Backspace') key = 'BackSpace';
    if (key === 'Delete') key = 'Delete';
    if (key.startsWith('F') && key.length <= 3) key = event.key; // Function keys

    const combination: KeyCombination = {
      ctrl: event.ctrlKey,
      alt: event.altKey,
      shift: event.shiftKey,
      meta: event.metaKey,
      key,
    };

    // Validate the combination
    if (!combination.ctrl && !combination.alt && !combination.shift && !combination.meta) {
      setError('Shortcut must include at least one modifier key (Ctrl, Alt, Shift, or Cmd)');
      return;
    }

    setError('');
    setDetectedKeys(combination);
    setIsListening(false);
  }, [isListening]);

  useEffect(() => {
    if (open && isListening) {
      document.addEventListener('keydown', handleKeyDown, true);
      return () => {
        document.removeEventListener('keydown', handleKeyDown, true);
      };
    }
  }, [open, isListening, handleKeyDown]);

  const startListening = () => {
    setDetectedKeys(null);
    setError('');
    setIsListening(true);
  };

  const handleSave = () => {
    if (detectedKeys) {
      const shortcut = formatShortcut(detectedKeys);
      onSave(shortcut);
      onClose();
    }
  };

  const handleCancel = () => {
    setDetectedKeys(null);
    setError('');
    setIsListening(false);
    onClose();
  };

  const handleDialogClose = () => {
    if (!isListening) {
      handleCancel();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleDialogClose}
      maxWidth="sm"
      fullWidth
      disableEscapeKeyDown={isListening}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Keyboard />
          Set {title}
        </Box>
      </DialogTitle>
      
      <DialogContent>
        <Box display="flex" flexDirection="column" gap={2}>
          {currentShortcut && (
            <Box>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Current shortcut:
              </Typography>
              <Chip 
                label={formatDisplayShortcut(currentShortcut)} 
                variant="outlined" 
                size="small"
              />
            </Box>
          )}

          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Press a key combination to set the new shortcut:
            </Typography>
            
            {!isListening && !detectedKeys && (
              <Button
                variant="outlined"
                onClick={startListening}
                startIcon={<Keyboard />}
                fullWidth
                sx={{ py: 2 }}
              >
                Click to Record Shortcut
              </Button>
            )}

            {isListening && (
              <Box
                sx={{
                  border: 2,
                  borderColor: 'primary.main',
                  borderRadius: 1,
                  p: 3,
                  textAlign: 'center',
                  bgcolor: 'action.hover',
                }}
              >
                <Typography variant="h6" color="primary">
                  Press your key combination...
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Include at least one modifier (Ctrl, Alt, Shift, or Cmd)
                </Typography>
              </Box>
            )}

            {detectedKeys && !isListening && (
              <Box>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Detected shortcut:
                </Typography>
                <Chip 
                  label={formatDisplayShortcut(formatShortcut(detectedKeys))} 
                  color="primary"
                  size="medium"
                />
              </Box>
            )}

            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}
          </Box>

          <Alert severity="info">
            <Typography variant="body2">
              <strong>Tips:</strong>
              <br />
              • Use Ctrl/Cmd + other keys for global shortcuts
              <br />
              • Avoid common system shortcuts (Ctrl+C, Ctrl+V, etc.)
              <br />
              • Function keys (F1-F12) work well for shortcuts
            </Typography>
          </Alert>
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleCancel} disabled={isListening}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained"
          disabled={!detectedKeys || isListening}
        >
          Save Shortcut
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ShortcutDialog;
