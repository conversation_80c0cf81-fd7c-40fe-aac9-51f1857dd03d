import React, { useState } from 'react';
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Badge,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  History,
  Star,
  Settings as SettingsIcon,
  Clear,
  PlayArrow,
  Stop,
} from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import { clearHistory, startMonitoring, stopMonitoring } from '@/store/slices/clipboardSlice';
import ClipboardHistoryList from './ClipboardHistoryList';
import Settings from './Settings';

const DRAWER_WIDTH = 240;

interface AppLayoutProps {
  children?: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const [mobileOpen, setMobileOpen] = useState(false);
  const [currentView, setCurrentView] = useState<'history' | 'favorites' | 'settings'>('history');
  
  const { history, favorites, isMonitoring } = useAppSelector((state) => state.clipboard);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleClearHistory = () => {
    if (window.confirm('Are you sure you want to clear all clipboard history?')) {
      dispatch(clearHistory());
    }
  };

  const handleToggleMonitoring = () => {
    if (isMonitoring) {
      dispatch(stopMonitoring());
    } else {
      dispatch(startMonitoring());
    }
  };

  const menuItems = [
    {
      id: 'history',
      label: 'Clipboard History',
      icon: <History />,
      badge: history.length,
    },
    {
      id: 'favorites',
      label: 'Favorites',
      icon: <Star />,
      badge: favorites.length,
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <SettingsIcon />,
    },
  ];

  const drawer = (
    <Box>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          Paste King
        </Typography>
      </Toolbar>
      <Divider />
      
      <List>
        {menuItems.map((item) => (
          <ListItem key={item.id} disablePadding>
            <ListItemButton
              selected={currentView === item.id}
              onClick={() => setCurrentView(item.id as any)}
            >
              <ListItemIcon>
                {item.badge !== undefined ? (
                  <Badge badgeContent={item.badge} color="primary">
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </ListItemIcon>
              <ListItemText primary={item.label} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
      
      <Divider />
      
      <List>
        <ListItem disablePadding>
          <ListItemButton onClick={handleToggleMonitoring}>
            <ListItemIcon>
              {isMonitoring ? <Stop color="error" /> : <PlayArrow color="success" />}
            </ListItemIcon>
            <ListItemText 
              primary={isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
              secondary={isMonitoring ? 'Clipboard monitoring active' : 'Clipboard monitoring paused'}
            />
          </ListItemButton>
        </ListItem>
        
        <ListItem disablePadding>
          <Tooltip title="Clear all clipboard history">
            <ListItemButton onClick={handleClearHistory} disabled={history.length === 0}>
              <ListItemIcon>
                <Clear color={history.length === 0 ? 'disabled' : 'error'} />
              </ListItemIcon>
              <ListItemText primary="Clear History" />
            </ListItemButton>
          </Tooltip>
        </ListItem>
      </List>
    </Box>
  );

  const renderMainContent = () => {
    switch (currentView) {
      case 'history':
        return <ClipboardHistoryList showFavoritesOnly={false} />;
      case 'favorites':
        return <ClipboardHistoryList showFavoritesOnly={true} />;
      case 'settings':
        return <Settings />;
      default:
        return children;
    }
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${DRAWER_WIDTH}px)` },
          ml: { sm: `${DRAWER_WIDTH}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {currentView === 'history' && 'Clipboard History'}
            {currentView === 'favorites' && 'Favorites'}
            {currentView === 'settings' && 'Settings'}
          </Typography>
          
          <Box display="flex" alignItems="center" gap={1}>
            <Badge
              color={isMonitoring ? 'success' : 'error'}
              variant="dot"
              sx={{
                '& .MuiBadge-badge': {
                  right: -3,
                  top: 3,
                },
              }}
            >
              <Typography variant="body2" color="inherit">
                {isMonitoring ? 'Monitoring' : 'Paused'}
              </Typography>
            </Badge>
          </Box>
        </Toolbar>
      </AppBar>
      
      <Box
        component="nav"
        sx={{ width: { sm: DRAWER_WIDTH }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: DRAWER_WIDTH },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: DRAWER_WIDTH },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${DRAWER_WIDTH}px)` },
          height: '100vh',
          overflow: 'hidden',
        }}
      >
        <Toolbar />
        <Box height="calc(100vh - 64px)" overflow="hidden">
          {renderMainContent()}
        </Box>
      </Box>
    </Box>
  );
};

export default AppLayout;
