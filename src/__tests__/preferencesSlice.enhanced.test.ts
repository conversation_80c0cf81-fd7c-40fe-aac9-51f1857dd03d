import { configureStore } from '@reduxjs/toolkit';
import preferencesSlice, {
  loadPreferences,
  savePreferences,
  setAutoStart,
  setShowInSystemTray,
  setMaxHistoryLength,
  updateShortcut,
  resetShortcuts,
} from '@/store/slices/preferencesSlice';
import type { PreferencesState } from '@/types/clipboard';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}));

const mockInvoke = vi.mocked(await import('@tauri-apps/api/core')).invoke;

describe('Enhanced Preferences Slice', () => {
  let store: any;

  const initialState: PreferencesState = {
    theme: 'dark' as const,
    shortcuts: {
      toggleApp: 'CommandOrControl+Shift+V',
      clearHistory: 'CommandOrControl+Shift+Delete',
      toggleFavorite: 'CommandOrControl+D',
      copySelected: 'Enter',
      deleteSelected: 'Delete',
      search: 'CommandOrControl+F',
      navigateUp: 'ArrowUp',
      navigateDown: 'ArrowDown',
    },
    autoStart: false,
    showInSystemTray: true,
    maxHistoryLength: 100,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    store = configureStore({
      reducer: {
        preferences: preferencesSlice,
      },
    });
  });

  describe('synchronous actions', () => {
    it('should handle setAutoStart', () => {
      store.dispatch(setAutoStart(true));
      const state = store.getState().preferences as PreferencesState;
      expect(state.autoStart).toBe(true);
    });

    it('should handle setShowInSystemTray', () => {
      store.dispatch(setShowInSystemTray(false));
      const state = store.getState().preferences as PreferencesState;
      expect(state.showInSystemTray).toBe(false);
    });

    it('should handle setMaxHistoryLength', () => {
      store.dispatch(setMaxHistoryLength(200));
      const state = store.getState().preferences as PreferencesState;
      expect(state.maxHistoryLength).toBe(200);
    });

    it('should handle updateShortcut', () => {
      store.dispatch(updateShortcut({ key: 'toggleApp', value: 'CommandOrControl+Alt+V' }));
      const state = store.getState().preferences as PreferencesState;
      expect(state.shortcuts.toggleApp).toBe('CommandOrControl+Alt+V');
    });

    it('should handle resetShortcuts', () => {
      // First modify a shortcut
      store.dispatch(updateShortcut({ key: 'toggleApp', value: 'CommandOrControl+Alt+V' }));
      
      // Then reset
      store.dispatch(resetShortcuts());
      const state = store.getState().preferences as PreferencesState;
      expect(state.shortcuts.toggleApp).toBe('CommandOrControl+Shift+V');
    });
  });

  describe('async actions', () => {
    it('should handle loadPreferences.fulfilled', async () => {
      const mockPreferences = {
        ...initialState,
        autoStart: true,
        maxHistoryLength: 150,
      };

      mockInvoke.mockResolvedValueOnce(mockPreferences);

      await store.dispatch(loadPreferences() as any);
      const state = store.getState().preferences as PreferencesState;
      
      expect(state.autoStart).toBe(true);
      expect(state.maxHistoryLength).toBe(150);
      expect(mockInvoke).toHaveBeenCalledWith('get_preferences');
    });

    it('should handle savePreferences.fulfilled', async () => {
      const updatedPreferences = {
        ...initialState,
        autoStart: true,
        showInSystemTray: false,
      };

      mockInvoke.mockResolvedValueOnce(updatedPreferences);

      await store.dispatch(savePreferences(updatedPreferences) as any);
      const state = store.getState().preferences as PreferencesState;
      
      expect(state.autoStart).toBe(true);
      expect(state.showInSystemTray).toBe(false);
      expect(mockInvoke).toHaveBeenCalledWith('save_preferences', { preferences: updatedPreferences });
    });

    it('should handle loadPreferences.rejected', async () => {
      mockInvoke.mockRejectedValueOnce(new Error('Failed to load'));

      try {
        await store.dispatch(loadPreferences() as any).unwrap();
      } catch (error) {
        expect(error).toMatchObject({
          message: 'Failed to load'
        });
      }

      // State should remain unchanged
      const state = store.getState().preferences as PreferencesState;
      expect(state).toEqual(initialState);
    });

    it('should handle savePreferences.rejected', async () => {
      const updatedPreferences = {
        ...initialState,
        autoStart: true,
      };

      mockInvoke.mockRejectedValueOnce(new Error('Failed to save'));

      try {
        await store.dispatch(savePreferences(updatedPreferences) as any).unwrap();
      } catch (error) {
        expect(error).toMatchObject({
          message: 'Failed to save'
        });
      }

      // State should remain unchanged
      const state = store.getState().preferences as PreferencesState;
      expect(state).toEqual(initialState);
    });
  });

  describe('edge cases', () => {
    it('should handle partial preference updates', async () => {
      const partialPreferences = {
        autoStart: true,
        maxHistoryLength: 250,
      };

      mockInvoke.mockResolvedValueOnce({
        ...initialState,
        ...partialPreferences,
      });

      await store.dispatch(savePreferences(partialPreferences as any) as any);
      const state = store.getState().preferences as PreferencesState;
      
      expect(state.autoStart).toBe(true);
      expect(state.maxHistoryLength).toBe(250);
      expect(state.showInSystemTray).toBe(true); // Should retain original value
    });

    it('should handle invalid shortcut keys gracefully', () => {
      // This should not crash even with invalid key
      store.dispatch(updateShortcut({ key: 'invalidKey' as any, value: 'Ctrl+X' }));
      
      const state = store.getState().preferences as PreferencesState;
      // Original shortcuts should remain unchanged
      expect(state.shortcuts.toggleApp).toBe('CommandOrControl+Shift+V');
    });

    it('should handle boundary values for maxHistoryLength', () => {
      // Test minimum value
      store.dispatch(setMaxHistoryLength(1));
      expect(store.getState().preferences.maxHistoryLength).toBe(1);

      // Test large value
      store.dispatch(setMaxHistoryLength(10000));
      expect(store.getState().preferences.maxHistoryLength).toBe(10000);

      // Test zero (should still work)
      store.dispatch(setMaxHistoryLength(0));
      expect(store.getState().preferences.maxHistoryLength).toBe(0);
    });
  });

  describe('state consistency', () => {
    it('should maintain state consistency across multiple updates', () => {
      // Perform multiple updates
      store.dispatch(setAutoStart(true));
      store.dispatch(setShowInSystemTray(false));
      store.dispatch(setMaxHistoryLength(300));
      store.dispatch(updateShortcut({ key: 'toggleApp', value: 'F1' }));
      store.dispatch(updateShortcut({ key: 'search', value: 'F2' }));

      const state = store.getState().preferences as PreferencesState;
      
      expect(state.autoStart).toBe(true);
      expect(state.showInSystemTray).toBe(false);
      expect(state.maxHistoryLength).toBe(300);
      expect(state.shortcuts.toggleApp).toBe('F1');
      expect(state.shortcuts.search).toBe('F2');
      
      // Other shortcuts should remain unchanged
      expect(state.shortcuts.clearHistory).toBe('CommandOrControl+Shift+Delete');
      expect(state.shortcuts.toggleFavorite).toBe('CommandOrControl+D');
    });

    it('should handle rapid successive updates', () => {
      // Simulate rapid updates that might happen in UI
      for (let i = 0; i < 10; i++) {
        store.dispatch(setMaxHistoryLength(100 + i));
      }

      const state = store.getState().preferences;
      expect(state.maxHistoryLength).toBe(109); // Last update should win
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete settings reset workflow', async () => {
      // First, modify all settings
      store.dispatch(setAutoStart(true));
      store.dispatch(setShowInSystemTray(false));
      store.dispatch(setMaxHistoryLength(500));
      store.dispatch(updateShortcut({ key: 'toggleApp', value: 'F12' }));

      // Verify changes
      let state = store.getState().preferences;
      expect(state.autoStart).toBe(true);
      expect(state.showInSystemTray).toBe(false);
      expect(state.maxHistoryLength).toBe(500);
      expect(state.shortcuts.toggleApp).toBe('F12');

      // Mock loading default preferences
      mockInvoke.mockResolvedValueOnce(initialState);

      // Reset by loading defaults
      await store.dispatch(loadPreferences() as any);

      // Verify reset
      state = store.getState().preferences as PreferencesState;
      expect(state.autoStart).toBe(false);
      expect(state.showInSystemTray).toBe(true);
      expect(state.maxHistoryLength).toBe(100);
      expect(state.shortcuts.toggleApp).toBe('CommandOrControl+Shift+V');
    });

    it('should handle save and reload cycle', async () => {
      const customPreferences = {
        ...initialState,
        autoStart: true,
        showInSystemTray: false,
        maxHistoryLength: 250,
        shortcuts: {
          ...initialState.shortcuts,
          toggleApp: 'F5',
          search: 'F6',
        },
      };

      // Save custom preferences
      mockInvoke.mockResolvedValueOnce(customPreferences);
      await store.dispatch(savePreferences(customPreferences) as any);

      // Verify save
      let state = store.getState().preferences as PreferencesState;
      expect(state.autoStart).toBe(true);
      expect(state.shortcuts.toggleApp).toBe('F5');

      // Reload preferences
      mockInvoke.mockResolvedValueOnce(customPreferences);
      await store.dispatch(loadPreferences() as any);

      // Verify reload
      state = store.getState().preferences as PreferencesState;
      expect(state.autoStart).toBe(true);
      expect(state.showInSystemTray).toBe(false);
      expect(state.maxHistoryLength).toBe(250);
      expect(state.shortcuts.toggleApp).toBe('F5');
      expect(state.shortcuts.search).toBe('F6');
    });
  });
});
