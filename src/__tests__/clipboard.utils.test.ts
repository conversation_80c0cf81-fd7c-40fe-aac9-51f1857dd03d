import {
  formatRelativeTime,
  truncateText,
  formatFileSize,
  detectContentType,
  filterEntries,
  sortEntries,
  validateClipboardEntry,
  generatePreview,
  copyToClipboard,
  debounce,
} from '@/utils/clipboard';
import type { ClipboardEntry } from '@/types/clipboard';

// Mock navigator.clipboard
Object.assign(navigator, {
  clipboard: {
    writeText: vi.fn(() => Promise.resolve()),
  },
});

// Mock document.execCommand
Object.assign(document, {
  execCommand: vi.fn(() => true),
  createElement: vi.fn(() => ({
    value: '',
    style: {},
    select: vi.fn(),
  })),
});

// Mock document.body methods
Object.assign(document.body, {
  appendChild: vi.fn(),
  removeChild: vi.fn(),
});

const mockEntries: ClipboardEntry[] = [
  {
    id: '1',
    content: 'First entry',
    type: 'text',
    timestamp: Date.now() - 1000,
    isFavorite: false,
    metadata: { source: 'app1' },
  },
  {
    id: '2',
    content: 'Second entry',
    type: 'text',
    timestamp: Date.now() - 2000,
    isFavorite: true,
    metadata: { source: 'app2', format: 'plain' },
  },
  {
    id: '3',
    content: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
    type: 'image',
    timestamp: Date.now() - 3000,
    isFavorite: false,
  },
];

describe('clipboard utils', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('formatRelativeTime', () => {
    it('returns "Just now" for very recent timestamps', () => {
      const now = Date.now();
      expect(formatRelativeTime(now)).toBe('Just now');
      expect(formatRelativeTime(now - 30000)).toBe('Just now'); // 30 seconds ago
    });

    it('returns minutes for timestamps within an hour', () => {
      const now = Date.now();
      expect(formatRelativeTime(now - 2 * 60 * 1000)).toBe('2m ago');
      expect(formatRelativeTime(now - 30 * 60 * 1000)).toBe('30m ago');
    });

    it('returns hours for timestamps within a day', () => {
      const now = Date.now();
      expect(formatRelativeTime(now - 2 * 60 * 60 * 1000)).toBe('2h ago');
      expect(formatRelativeTime(now - 12 * 60 * 60 * 1000)).toBe('12h ago');
    });

    it('returns days for timestamps within a week', () => {
      const now = Date.now();
      expect(formatRelativeTime(now - 2 * 24 * 60 * 60 * 1000)).toBe('2d ago');
      expect(formatRelativeTime(now - 5 * 24 * 60 * 60 * 1000)).toBe('5d ago');
    });

    it('returns formatted date for older timestamps', () => {
      const oldDate = new Date('2023-01-01');
      const result = formatRelativeTime(oldDate.getTime());
      expect(result).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/); // MM/DD/YYYY format
    });
  });

  describe('truncateText', () => {
    it('returns original text if shorter than max length', () => {
      const text = 'Short text';
      expect(truncateText(text, 50)).toBe(text);
    });

    it('truncates text and adds ellipsis if longer than max length', () => {
      const text = 'This is a very long text that should be truncated';
      expect(truncateText(text, 20)).toBe('This is a very long ...');
    });

    it('uses default max length of 200', () => {
      const text = 'A'.repeat(250);
      const result = truncateText(text);
      expect(result).toBe('A'.repeat(200) + '...');
    });

    it('handles empty string', () => {
      expect(truncateText('')).toBe('');
    });
  });

  describe('formatFileSize', () => {
    it('formats bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 B');
      expect(formatFileSize(500)).toBe('500 B');
      expect(formatFileSize(1023)).toBe('1023 B');
    });

    it('formats kilobytes correctly', () => {
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(2048)).toBe('2 KB');
    });

    it('formats megabytes correctly', () => {
      expect(formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(formatFileSize(1.5 * 1024 * 1024)).toBe('1.5 MB');
    });

    it('formats gigabytes correctly', () => {
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
      expect(formatFileSize(2.5 * 1024 * 1024 * 1024)).toBe('2.5 GB');
    });
  });

  describe('detectContentType', () => {
    it('detects image content', () => {
      expect(detectContentType('data:image/png;base64,abc123')).toBe('image');
      expect(detectContentType('photo.jpg')).toBe('image');
      expect(detectContentType('image.PNG')).toBe('image');
      expect(detectContentType('graphic.svg')).toBe('image');
    });

    it('detects file content', () => {
      expect(detectContentType('C:\\Users\\<USER>