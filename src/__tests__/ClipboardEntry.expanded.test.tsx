import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import ClipboardEntry from '@/components/ClipboardEntry';
import clipboardSlice from '@/store/slices/clipboardSlice';
import preferencesSlice from '@/store/slices/preferencesSlice';
import type { ClipboardEntry as ClipboardEntryType } from '@/types/clipboard';

// Mock Tauri API is handled in setup.ts

const createMockStore = () => {
  return configureStore({
    reducer: {
      clipboard: clipboardSlice,
      preferences: preferencesSlice,
    },
  });
};

const mockEntry: ClipboardEntryType = {
  id: 'test-id',
  content: 'This is a very long text content that should be truncated when displayed initially and then expanded when the user clicks the show more button. This text is definitely longer than 200 characters to test the expansion functionality properly.',
  type: 'text',
  timestamp: Date.now(),
  isFavorite: false,
};

const mockShortEntry: ClipboardEntryType = {
  id: 'test-short-id',
  content: 'Short text',
  type: 'text',
  timestamp: Date.now(),
  isFavorite: false,
};

describe('ClipboardEntry Expandable Text', () => {
  let store: ReturnType<typeof createMockStore>;
  const mockOnClick = vi.fn();
  const mockOnCopy = vi.fn();

  beforeEach(() => {
    store = createMockStore();
    vi.clearAllMocks();
  });

  it('should show "Show More" button for long content', () => {
    render(
      <Provider store={store}>
        <ClipboardEntry
          entry={mockEntry}
          isSelected={false}
          onClick={mockOnClick}
          onCopy={mockOnCopy}
        />
      </Provider>
    );

    expect(screen.getByText('Show More')).toBeInTheDocument();
    expect(screen.getByText(/This is a very long text content/)).toBeInTheDocument();
    expect(screen.getByText(/\.\.\./)).toBeInTheDocument();
  });

  it('should not show "Show More" button for short content', () => {
    render(
      <Provider store={store}>
        <ClipboardEntry
          entry={mockShortEntry}
          isSelected={false}
          onClick={mockOnClick}
          onCopy={mockOnCopy}
        />
      </Provider>
    );

    expect(screen.queryByText('Show More')).not.toBeInTheDocument();
    expect(screen.getByText('Short text')).toBeInTheDocument();
  });

  it('should expand content when "Show More" is clicked', () => {
    render(
      <Provider store={store}>
        <ClipboardEntry
          entry={mockEntry}
          isSelected={false}
          onClick={mockOnClick}
          onCopy={mockOnCopy}
        />
      </Provider>
    );

    const showMoreButton = screen.getByText('Show More');
    fireEvent.click(showMoreButton);

    expect(screen.getByText('Show Less')).toBeInTheDocument();
    expect(screen.getByText(/test the expansion functionality properly\./)).toBeInTheDocument();
    expect(screen.queryByText(/\.\.\./)).not.toBeInTheDocument();
  });

  it('should collapse content when "Show Less" is clicked', () => {
    render(
      <Provider store={store}>
        <ClipboardEntry
          entry={mockEntry}
          isSelected={false}
          onClick={mockOnClick}
          onCopy={mockOnCopy}
        />
      </Provider>
    );

    // First expand
    const showMoreButton = screen.getByText('Show More');
    fireEvent.click(showMoreButton);

    // Then collapse
    const showLessButton = screen.getByText('Show Less');
    fireEvent.click(showLessButton);

    expect(screen.getByText('Show More')).toBeInTheDocument();
    expect(screen.getByText(/\.\.\./)).toBeInTheDocument();
    expect(screen.queryByText(/test the expansion functionality properly\./)).not.toBeInTheDocument();
  });

  it('should not trigger onClick when expand button is clicked', () => {
    render(
      <Provider store={store}>
        <ClipboardEntry
          entry={mockEntry}
          isSelected={false}
          onClick={mockOnClick}
          onCopy={mockOnCopy}
        />
      </Provider>
    );

    const showMoreButton = screen.getByText('Show More');
    fireEvent.click(showMoreButton);

    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it('should toggle favorite when star button is clicked', () => {
    render(
      <Provider store={store}>
        <ClipboardEntry
          entry={mockEntry}
          isSelected={false}
          onClick={mockOnClick}
          onCopy={mockOnCopy}
        />
      </Provider>
    );

    const favoriteButton = screen.getByRole('button', { name: /add to favorites/i });
    fireEvent.click(favoriteButton);

    // Check that the action was dispatched (we can't easily test the actual dispatch without more complex mocking)
    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it('should show favorite icon when entry is favorited', () => {
    const favoritedEntry = { ...mockEntry, isFavorite: true };
    
    render(
      <Provider store={store}>
        <ClipboardEntry
          entry={favoritedEntry}
          isSelected={false}
          onClick={mockOnClick}
          onCopy={mockOnCopy}
        />
      </Provider>
    );

    expect(screen.getByRole('button', { name: /remove from favorites/i })).toBeInTheDocument();
  });
});
