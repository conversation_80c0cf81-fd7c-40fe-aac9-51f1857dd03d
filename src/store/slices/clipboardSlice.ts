import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { invoke } from '@tauri-apps/api/core';
import type { ClipboardEntry, ClipboardState } from '@/types/clipboard';

const initialState: ClipboardState = {
  history: [],
  favorites: [],
  maxHistoryLength: 100,
  isMonitoring: false,
  searchQuery: '',
  selectedEntryId: null,
};

// Async thunks for Tau<PERSON> commands
export const getClipboardHistory = createAsyncThunk(
  'clipboard/getHistory',
  async () => {
    const history = await invoke<ClipboardEntry[]>('get_clipboard_history');
    return history;
  }
);

export const saveClipboardEntry = createAsyncThunk(
  'clipboard/saveEntry',
  async (entry: Omit<ClipboardEntry, 'id' | 'timestamp'>) => {
    const savedEntry = await invoke<ClipboardEntry>('save_clipboard_entry', { entry });
    return savedEntry;
  }
);

export const toggleFavorite = createAsyncThunk(
  'clipboard/toggleFavorite',
  async (entryId: string) => {
    const updatedEntry = await invoke<ClipboardEntry>('toggle_favorite', { entryId });
    return updatedEntry;
  }
);

export const clearHistory = createAsyncThunk(
  'clipboard/clearHistory',
  async () => {
    await invoke('clear_clipboard_history');
  }
);

export const deleteEntry = createAsyncThunk(
  'clipboard/deleteEntry',
  async (entryId: string) => {
    await invoke('delete_clipboard_entry', { entryId });
    return entryId;
  }
);

export const startMonitoring = createAsyncThunk(
  'clipboard/startMonitoring',
  async () => {
    await invoke('start_clipboard_monitoring');
  }
);

export const stopMonitoring = createAsyncThunk(
  'clipboard/stopMonitoring',
  async () => {
    await invoke('stop_clipboard_monitoring');
  }
);

export const checkMonitoringStatus = createAsyncThunk(
  'clipboard/checkMonitoringStatus',
  async () => {
    const isMonitoring = await invoke<boolean>('is_monitoring_clipboard');
    return isMonitoring;
  }
);

const clipboardSlice = createSlice({
  name: 'clipboard',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setSelectedEntry: (state, action: PayloadAction<string | null>) => {
      state.selectedEntryId = action.payload;
    },
    addEntry: (state, action: PayloadAction<ClipboardEntry>) => {
      // Add to beginning of history
      state.history.unshift(action.payload);
      
      // Trim history if it exceeds max length
      if (state.history.length > state.maxHistoryLength) {
        state.history = state.history.slice(0, state.maxHistoryLength);
      }
    },
    updateMaxHistoryLength: (state, action: PayloadAction<number>) => {
      state.maxHistoryLength = action.payload;
      
      // Trim current history if needed
      if (state.history.length > action.payload) {
        state.history = state.history.slice(0, action.payload);
      }
    },
    syncMaxHistoryLength: (state, action: PayloadAction<number>) => {
      state.maxHistoryLength = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getClipboardHistory.fulfilled, (state, action) => {
        const payload = Array.isArray(action.payload) ? action.payload : [];
        state.history = payload;
        state.favorites = payload.filter(entry => entry.isFavorite);
      })
      .addCase(saveClipboardEntry.fulfilled, (state, action) => {
        state.history.unshift(action.payload);
        if (state.history.length > state.maxHistoryLength) {
          state.history = state.history.slice(0, state.maxHistoryLength);
        }
      })
      .addCase(toggleFavorite.fulfilled, (state, action) => {
        const entry = action.payload;
        
        if (!entry) return;
        
        // Update in history
        const historyIndex = state.history.findIndex(item => item.id === entry.id);
        if (historyIndex !== -1) {
          state.history[historyIndex] = entry;
        }
        
        // Update favorites list
        if (entry.isFavorite) {
          const existingIndex = state.favorites.findIndex(item => item.id === entry.id);
          if (existingIndex === -1) {
            state.favorites.push(entry);
          } else {
            state.favorites[existingIndex] = entry;
          }
        } else {
          state.favorites = state.favorites.filter(item => item.id !== entry.id);
        }
      })
      .addCase(clearHistory.fulfilled, (state) => {
        state.history = [];
        state.selectedEntryId = null;
      })
      .addCase(deleteEntry.fulfilled, (state, action) => {
        const entryId = action.payload;
        state.history = state.history.filter(entry => entry.id !== entryId);
        state.favorites = state.favorites.filter(entry => entry.id !== entryId);
        
        if (state.selectedEntryId === entryId) {
          state.selectedEntryId = null;
        }
      })
      .addCase(startMonitoring.fulfilled, (state) => {
        state.isMonitoring = true;
      })
      .addCase(stopMonitoring.fulfilled, (state) => {
        state.isMonitoring = false;
      })
      .addCase(checkMonitoringStatus.fulfilled, (state, action) => {
        state.isMonitoring = action.payload;
      });
  },
});

export const {
  setSearchQuery,
  setSelectedEntry,
  addEntry,
  updateMaxHistoryLength,
  syncMaxHistoryLength,
} = clipboardSlice.actions;

export default clipboardSlice.reducer;
