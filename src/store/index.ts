import { configureStore, createListenerMiddleware } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import clipboardSlice, { syncMaxHistoryLength } from './slices/clipboardSlice';
import preferencesSlice, { loadPreferences, savePreferences } from './slices/preferencesSlice';
import type { RootState } from '@/types/clipboard';

// Create listener middleware for syncing max history length
const listenerMiddleware = createListenerMiddleware();

// Sync max history length when preferences are loaded or saved
listenerMiddleware.startListening({
  actionCreator: loadPreferences.fulfilled,
  effect: (action, listenerApi) => {
    const maxHistoryLength = action.payload?.maxHistoryLength;
    if (maxHistoryLength !== undefined) {
      listenerApi.dispatch(syncMaxHistoryLength(maxHistoryLength));
    }
  },
});

listenerMiddleware.startListening({
  actionCreator: savePreferences.fulfilled,
  effect: (action, listenerApi) => {
    const maxHistoryLength = action.payload?.maxHistoryLength;
    if (maxHistoryLength !== undefined) {
      listenerApi.dispatch(syncMaxHistoryLength(maxHistoryLength));
    }
  },
});

export const store = configureStore({
  reducer: {
    clipboard: clipboardSlice,
    preferences: preferencesSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).prepend(listenerMiddleware.middleware),
});

export type AppDispatch = typeof store.dispatch;
export type AppRootState = RootState;

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<AppRootState> = useSelector;

export default store;
