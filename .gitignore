# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
jspm_packages/

# Build outputs
dist/
dist-ssr/
build/
out/

# Tauri build outputs
src-tauri/target/
src-tauri/gen/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Testing
coverage/
*.lcov
.nyc_output

# Cache directories
.cache/
.parcel-cache/
.vite/
.eslintcache
.stylelintcache

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Rust specific
# Cargo lock file (uncomment if you want to ignore it)
# Cargo.lock

# Backup files
*.bak
*.backup
*.tmp

# TypeScript
*.tsbuildinfo

# Temporary folders
tmp/
temp/

# Storybook build outputs
storybook-static

# Tauri specific
# WiX generated files
*.wixobj
*.wixpdb

# macOS
.AppleDouble
.LSOverride

# Windows
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*.AppImage
*.deb
*.rpm

# Application specific
# Add any project-specific files or directories here
