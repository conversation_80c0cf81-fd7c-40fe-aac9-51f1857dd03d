mod clipboard;
mod clipboard_monitor;

use clipboard::{ClipboardManager, ClipboardEntry, Preferences};
use clipboard_monitor::ClipboardMonitor;
use std::sync::{Arc, Mutex};
use tauri::{State, Manager};

// Application state
pub struct AppState {
    clipboard_manager: Arc<ClipboardManager>,
    clipboard_monitor: Arc<ClipboardMonitor>,
    system_tray_enabled: Arc<Mutex<bool>>,
}

// <PERSON><PERSON> commands
#[tauri::command]
async fn get_clipboard_history(state: State<'_, AppState>) -> Result<Vec<ClipboardEntry>, String> {
    state.clipboard_manager.get_history()
}

#[tauri::command]
async fn save_clipboard_entry(
    entry: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    let content = entry["content"].as_str().unwrap_or("").to_string();
    let entry_type = entry["type"].as_str().unwrap_or("text").to_string();
    state.clipboard_manager.add_entry(content, entry_type)
}

#[tauri::command]
async fn toggle_favorite(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<ClipboardEntry, String> {
    state.clipboard_manager.toggle_favorite(&entry_id)
}

#[tauri::command]
async fn delete_clipboard_entry(
    entry_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state.clipboard_manager.delete_entry(&entry_id)
}

#[tauri::command]
async fn clear_clipboard_history(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_manager.clear_history()
}

#[tauri::command]
async fn get_preferences(state: State<'_, AppState>) -> Result<Preferences, String> {
    state.clipboard_manager.get_preferences()
}

#[tauri::command]
async fn save_preferences(
    preferences: Preferences,
    state: State<'_, AppState>,
) -> Result<Preferences, String> {
    state.clipboard_manager.update_preferences(preferences)
}

#[tauri::command]
async fn start_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_monitor.start_monitoring().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn stop_clipboard_monitoring(state: State<'_, AppState>) -> Result<(), String> {
    state.clipboard_monitor.stop_monitoring().map_err(|e| e.to_string())
}

#[tauri::command]
async fn is_monitoring_clipboard(state: State<'_, AppState>) -> Result<bool, String> {
    Ok(state.clipboard_monitor.is_running())
}

#[tauri::command]
async fn update_global_shortcuts(shortcuts: serde_json::Value) -> Result<(), String> {
    // TODO: Implement global shortcut updates with the provided shortcuts
    println!("Updating global shortcuts: {:?}", shortcuts);
    Ok(())
}

#[tauri::command]
async fn set_auto_start(enabled: bool) -> Result<(), String> {
    use auto_launch::AutoLaunch;
    
    let auto = AutoLaunch::new(
        "paste-king",
        &std::env::current_exe().map_err(|e| e.to_string())?.to_string_lossy(),
        false, // use_launch_agent parameter for macOS
        &[] as &[&str],
    );
    
    if enabled {
        auto.enable().map_err(|e| e.to_string())?;
    } else {
        auto.disable().map_err(|e| e.to_string())?;
    }
    
    Ok(())
}

#[tauri::command]
async fn set_system_tray(enabled: bool, state: State<'_, AppState>) -> Result<(), String> {
    println!("System tray setting changed to: {}", enabled);
    
    // Update the state
    {
        let mut tray_enabled = state.system_tray_enabled.lock().map_err(|e| e.to_string())?;
        *tray_enabled = enabled;
    }
    
    // For now, just acknowledge the setting change
    // System tray functionality will be implemented based on the saved preference
    Ok(())
}

#[tauri::command]
async fn get_system_tray_status(state: State<'_, AppState>) -> Result<bool, String> {
    let tray_enabled = state.system_tray_enabled.lock().map_err(|e| e.to_string())?;
    Ok(*tray_enabled)
}

#[tauri::command]
async fn get_clipboard_status(state: State<'_, AppState>) -> Result<String, String> {
    let is_monitoring = state.clipboard_monitor.is_running();
    let history = state.clipboard_manager.get_history()?;
    Ok(format!("Monitoring: {}, History entries: {}", is_monitoring, history.len()))
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let clipboard_manager = Arc::new(ClipboardManager::new());
    let clipboard_monitor = Arc::new(
        ClipboardMonitor::new(clipboard_manager.clone())
            .expect("Failed to initialize clipboard monitor")
    );

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_store::Builder::default().build())
        .manage(AppState {
            clipboard_manager: clipboard_manager.clone(),
            clipboard_monitor: clipboard_monitor.clone(),
            system_tray_enabled: Arc::new(Mutex::new(true)), // Default to enabled
        })
        .invoke_handler(tauri::generate_handler![
            get_clipboard_history,
            save_clipboard_entry,
            toggle_favorite,
            delete_clipboard_entry,
            clear_clipboard_history,
            get_preferences,
            save_preferences,
            start_clipboard_monitoring,
            stop_clipboard_monitoring,
            is_monitoring_clipboard,
            get_clipboard_status,
            update_global_shortcuts,
            set_auto_start,
            set_system_tray,
            get_system_tray_status,
        ])
        .setup(move |app| {
            // Initialize clipboard monitoring
            let app_handle = app.handle().clone();
            let monitor_clone = clipboard_monitor.clone();

            // Load preferences and log system tray setting
            let clipboard_manager_clone = clipboard_manager.clone();
            tauri::async_runtime::spawn(async move {
                // Load preferences to check system tray setting
                if let Ok(preferences) = clipboard_manager_clone.get_preferences() {
                    println!("System tray enabled: {}", preferences.show_in_system_tray);
                    // System tray functionality can be implemented here in the future
                }
            });

            // Start clipboard monitoring automatically
            tauri::async_runtime::spawn(async move {
                if let Err(e) = monitor_clone.start_monitoring().await {
                    eprintln!("Failed to start clipboard monitoring: {}", e);
                } else {
                    println!("Clipboard monitoring started successfully");
                }
            });

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
