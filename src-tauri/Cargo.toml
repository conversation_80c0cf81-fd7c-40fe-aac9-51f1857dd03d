[package]
name = "paste-king"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "paste_king_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-clipboard-manager = "2.2.2"
tauri-plugin-global-shortcut = "2.2.1"
tauri-plugin-store = "2.2.0"
tauri-plugin-autostart = "2.2.0"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
uuid = { version = "1.0", features = ["v4"] }
tokio = { version = "1.0", features = ["full"] }
arboard = { version = "3.5", features = ["image"] }
log = "0.4"
base64 = "0.22"
dirs = "5.0"
auto-launch = "0.5"
